# Bulk User Deletion System - Testing Guide

## Overview
This document provides instructions for testing the newly implemented bulk user deletion system.

## Features Implemented

### 1. **Bulk Deletion DTOs**
- `BulkDeleteUsersDto`: Input validation for user IDs array (1-100 users)
- `BulkDeleteResponseDto`: Response with job ID and queue information
- `BulkDeletionStatsDto`: Statistics for email reporting

### 2. **Queue Processing**
- Added `BULK_DELETE_USERS` job type to `GeneralJobType`
- Implemented `BulkDeleteUsersJobData` interface
- Enhanced `GeneralProcessor` with bulk deletion logic

### 3. **AuthService Enhancement**
- Added `bulkDeleteUsers()` method
- Validates user IDs and prevents self-deletion
- Queues background job for processing

### 4. **Controller Endpoint**
- `DELETE /api/v1/auth/users/bulk`
- Super admin only access
- Accepts array of user IDs
- Returns job ID for tracking

### 5. **Email Reporting**
- Professional HTML template (`bulk-deletion-report.hbs`)
- Detailed statistics with success/failure breakdown
- Sent to admin who initiated the deletion

### 6. **Error Handling**
- Validates user existence
- Handles foreign key constraints
- Prevents admin self-deletion
- Comprehensive error reporting

## API Endpoint

### Request
```http
DELETE /api/v1/auth/users/bulk
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "userIds": ["uuid1", "uuid2", "uuid3"],
  "adminEmail": "<EMAIL>" // optional
}
```

### Response
```json
{
  "message": "Bulk user deletion job has been queued successfully",
  "jobId": "job_12345",
  "usersQueued": 3
}
```

## Testing Steps

### 1. **Prerequisites**
- Super admin user account
- Test user accounts to delete
- Email service configured
- Redis/BullMQ running

### 2. **Test Cases**

#### Valid Bulk Deletion
```bash
curl -X DELETE http://localhost:3000/api/v1/auth/users/bulk \
  -H "Authorization: Bearer <super_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userIds": ["test-user-1", "test-user-2"],
    "adminEmail": "<EMAIL>"
  }'
```

#### Invalid Permissions (Non-Super Admin)
```bash
curl -X DELETE http://localhost:3000/api/v1/auth/users/bulk \
  -H "Authorization: Bearer <regular_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userIds": ["test-user-1"]
  }'
```

#### Self-Deletion Prevention
```bash
curl -X DELETE http://localhost:3000/api/v1/auth/users/bulk \
  -H "Authorization: Bearer <super_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userIds": ["<super_admin_user_id>"]
  }'
```

### 3. **Monitor Job Progress**
- Check BullMQ dashboard at `/admin/queues`
- Monitor logs for processing updates
- Verify email delivery

### 4. **Verify Results**
- Check database for deleted users
- Verify related data cleanup
- Confirm email report received

## Security Features

1. **Role-Based Access**: Only super_admin can perform bulk deletions
2. **Self-Protection**: Prevents admin from deleting their own account
3. **Validation**: Validates user IDs and existence
4. **Audit Trail**: Comprehensive logging and email reporting
5. **Background Processing**: Prevents long request times

## Performance Considerations

1. **Batch Processing**: Users processed in batches of 10
2. **Queue-Based**: Background processing prevents timeouts
3. **Transaction Safety**: Each deletion wrapped in database transaction
4. **Error Isolation**: Failed deletions don't stop the entire process

## Monitoring

1. **Logs**: Check application logs for processing details
2. **Queue Dashboard**: Monitor job status in BullMQ UI
3. **Email Reports**: Automatic completion notifications
4. **Database**: Verify data cleanup and constraint handling
