import {
  Body,
  Controller,
  Logger,
  <PERSON>,
  Req,
  <PERSON>s,
  BadRequestException,
  Patch,
  UseGuards,
  Get,
  Query,
  ValidationPipe,
  Delete,
  Param,
} from '@nestjs/common';
import type { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { AppClients, AuthRoutes } from '@app/shared/constants/auth.constants';
import {
  LoginInputDto,
  OtpDto,
  UserQueryParams,
  VerifyMagicLinkDto,
  WaitingListResponse,
  ApproveWaitingListResponseDto,
  LoginResponseDto,
  VerifyOtpResponseDto,
  VerifyMagicLinkResponseDto,
  RefreshTokenResponseDto,
  RefreshTokenRequestDto,
  DeactivateAccountResponseDto,
  WaitingListResponseDto,
  ApproveUsersRequestDto,
  ErrorResponseDto,
  DeleteUserResponseDto,
  DeleteAccountConfirmationDto,
  DeleteAccountResponseDto,
  DeletedUserDto,
  BulkDeleteUsersDto,
  BulkDeleteResponseDto,
} from './dto/auth.dto';
import { createCookieOptionsWithPriority } from './helpers/cookie.helpers';
import { AuthControllerMessage } from '@app/shared/constants/auth.message';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiHeader,
  ApiParam,
} from '@nestjs/swagger';
import { Public } from '@/guards/guard.decorator';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { RoleGuard } from '@/guards/role.guard';
import { UseRoles } from 'nest-access-control';
import { User } from '@/guards/user.decorator';
import { type User as IUser } from '@/db/schema';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@Controller({ version: '1', path: 'auth' })
@ApiTags('Auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}
  private readonly logger = new Logger(AuthController.name);

  @ApiOperation({
    summary: 'Initiate authentication',
    description:
      'Sends OTP for mobile clients or magic link for web clients based on x-client-type header',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Client type - determines authentication method',
    enum: ['mobile', 'web'],
    required: true,
  })
  @ApiOkResponse({
    description: 'Authentication initiated successfully',
    type: LoginResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid email or user not found',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Access denied - students cannot use web login',
    type: ErrorResponseDto,
    example: { statusCode: 403, message: 'Access denied' },
  })
  @Post(AuthRoutes.LOGIN)
  @Public()
  async login(@Req() req: Request, @Body() loginInput: LoginInputDto) {
    try {
      const result = await this.authService.login(loginInput, req);

      if (result && typeof result === 'object' && 'statusCode' in result) {
        return result;
      }

      return {
        message:
          req.headers['x-client-type'] == AppClients.MOBILE
            ? AuthControllerMessage.OTP_SENT
            : AuthControllerMessage.MAGIC_LINK_SENT,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to process login for ${loginInput.email}`,
        error.stack,
      );
      throw error;
    }
  }
  @ApiOperation({
    summary: 'Verify OTP',
    description:
      'Verifies OTP and returns authentication tokens for mobile clients',
  })
  @ApiOkResponse({
    description: 'OTP verified successfully',
    type: VerifyOtpResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid or expired OTP',
    type: ErrorResponseDto,
  })
  @Post(AuthRoutes.VERIFY_OTP)
  @Public()
  async verifyOtp(
    @Res({ passthrough: true }) response: Response,
    @Body() otpDto: OtpDto,
  ) {
    try {
      const { accessToken, refreshToken, user, cookieOptions, deleted }: any =
        await this.authService.verifyOtp(otpDto.otp);

      response.set('Set-Cookie', [
        createCookieOptionsWithPriority(refreshToken, cookieOptions),
      ]);

      return { accessToken, user, deleted };
    } catch (error: any) {
      this.logger.error('Failed to verify OTP', error.stack);
      throw error;
    }
  }
  @ApiOperation({
    summary: 'Verify Magic Link',
    description:
      'Verifies magic link token and returns authentication tokens for web clients',
  })
  @ApiOkResponse({
    description: 'Magic link verified successfully',
    type: VerifyMagicLinkResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid or expired magic link',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Invalid token',
    type: ErrorResponseDto,
  })
  @Post(AuthRoutes.VERIFY_MAGIC_LINK)
  @Public()
  async verifyMagicLink(
    @Res({ passthrough: true }) response: Response,
    @Body() { token }: VerifyMagicLinkDto,
  ) {
    try {
      const { accessToken, refreshToken, user, cookieOptions } =
        await this.authService.verifyMagicLink(token);
      response.set('Set-Cookie', [
        createCookieOptionsWithPriority(refreshToken, cookieOptions),
      ]);
      return { accessToken, user };
    } catch (error: any) {
      this.logger.error('Failed to verify Magic Link', error.stack);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Refresh Access Token',
    description: 'Refreshes access token using refresh token',
  })
  @ApiOkResponse({
    description: 'Access token refreshed successfully',
    type: RefreshTokenResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Invalid refresh token',
    type: ErrorResponseDto,
  })
  @Post(AuthRoutes.REFRESH_TOKEN)
  async refreshAccessToken(
    @Req() req: Request,
    @Body() body: RefreshTokenRequestDto,
  ) {
    try {
      const accessToken = await this.authService.refreshAccessToken(
        req,
        body.refreshToken,
      );
      return { accessToken };
    } catch (error: any) {
      this.logger.error('Failed to refresh access token', error.stack);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Deactivate Account',
    description: 'Deactivates user account (requires authentication)',
  })
  @ApiOkResponse({
    description: 'Account deactivated successfully',
    type: DeactivateAccountResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: ErrorResponseDto,
  })
  @Patch(AuthRoutes.DEACTIVATE)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'update', possession: 'own' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async deactivate(@User() user: IUser): Promise<any> {
    try {
      await this.authService.deactivateAccount(user.id);
      this.logger.log('Account deactivated successfully for user:', user.id);
      return { message: 'Account deactivated successfully' };
    } catch (error: any) {
      this.logger.error('Failed to deactivate account', error.message);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Approve Users',
    description: 'Approves users from waiting list',
  })
  @ApiOkResponse({
    description: 'Users approved successfully',
    type: ApproveWaitingListResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'User IDs are required',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'No valid users found in waiting list',
    type: ErrorResponseDto,
  })
  @Patch(AuthRoutes.APPROVE_USER)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user-approval', action: 'update', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async approveUsers(
    @Body() body: ApproveUsersRequestDto,
  ): Promise<ApproveWaitingListResponseDto> {
    try {
      if (!body.userIds?.length) {
        throw new BadRequestException('User IDs are required');
      }

      const result = await this.authService.approveWaitingList(body.userIds);
      this.logger.log(`Batch approval completed: ${result.data} users`);
      return new ApproveWaitingListResponseDto(result);
    } catch (error) {
      this.logger.error('Failed to approve users', error);
      throw error;
    }
  }
  @ApiOperation({
    summary: 'Get Waiting List',
    description: 'Retrieves paginated list of users pending approval',
  })
  @ApiHeader({
    name: 'x-client-type',
    description: 'Must be web',
    enum: ['web'],
    required: true,
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field',
    required: false,
    type: String,
    example: 'id',
  })
  @ApiQuery({
    name: 'order',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
    example: 'asc',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term',
    required: false,
    type: String,
  })
  @ApiOkResponse({
    description: 'Users on the waiting list retrieved successfully',
    type: WaitingListResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ErrorResponseDto,
  })
  @Get(AuthRoutes.WAITING)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user-approval', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getAllUsers(
    @Query(new ValidationPipe({ transform: true })) filter: UserQueryParams,
  ): Promise<WaitingListResponse | undefined> {
    try {
      const result = await this.authService.getWaitingList(filter);
      this.logger.log(
        `Retrieved waiting list with ${result.pagination.total} total users`,
      );
      return result;
    } catch (error: any) {
      this.logger.error('Failed to get users', error.message);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Disapprove Users',
    description: 'Rejects and deletes users from waiting list',
  })
  @ApiOkResponse({
    description: 'Users rejected and deleted successfully',
    type: ApproveWaitingListResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'User IDs are required',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'No valid users found to disapprove',
    type: ErrorResponseDto,
  })
  @Patch(AuthRoutes.DISAPPROVE_USER)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user-approval', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async disApproveUsers(
    @Body() body: ApproveUsersRequestDto,
  ): Promise<ApproveWaitingListResponseDto> {
    try {
      if (!body.userIds?.length) {
        throw new BadRequestException('User IDs are required');
      }

      const result = await this.authService.disApproveWaitingList(body.userIds);
      this.logger.log(
        `Batch rejection completed: ${result.total} users deleted`,
      );
      return new ApproveWaitingListResponseDto({
        ...result,
        message: 'Users rejected and deleted successfully',
      });
    } catch (error: any) {
      this.logger.error('Failed to reject and delete users', error.message);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Notify Pending Users',
    description:
      'Sends notifications to pending users with non-academic emails',
  })
  @ApiOkResponse({
    description: 'Notifications sent to pending users with non-academic emails',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        count: { type: 'number' },
        failures: { type: 'number' },
        duration: { type: 'string' },
      },
    },
  })
  @Post(AuthRoutes.NOTIFY_PENDING_USERS)
  @CLIENT_TYPE(AppClients.WEB)
  async notifyPendingUsers() {
    try {
      // Process directly instead of using a queue
      const result = await this.authService.notifyPendingNonAcademicUsers();

      return {
        message: `Sent notifications to ${result.count} pending users with non-academic emails`,
        count: result.count,
        failures: result.failures,
        duration: result.duration,
      };
    } catch (error: any) {
      this.logger.error('Failed to notify pending users', error.message);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Delete Own Account',
    description:
      'Allows authenticated users to permanently delete their own account and all related data',
  })
  @ApiOkResponse({
    description: 'Account and all related data deleted successfully',
    type: DeleteAccountResponseDto,
  })
  @ApiBadRequestResponse({
    description:
      'Invalid confirmation text or cannot delete account due to constraints',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
    type: ErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User account not found',
    type: ErrorResponseDto,
  })
  @Delete(AuthRoutes.DELETE_ACCOUNT)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'own' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async deleteOwnAccount(
    @User() user: IUser,
    @Body() body: DeleteAccountConfirmationDto,
  ): Promise<DeleteAccountResponseDto> {
    try {
      const result = await this.authService.deleteOwnAccount(
        user.id,
        body.confirmationText,
      );
      this.logger.log(`Account deletion completed successfully: ${user.id}`);
      return {
        message: result.message,
        deletedUser: result.deletedUser as DeletedUserDto,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to delete account for user ${user.id}:`,
        error.message,
      );
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Delete User (SuperAdmin)',
    description:
      'Allows super_admin users to permanently delete any user and all related data',
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'User and all related data deleted successfully',
    type: DeleteUserResponseDto,
  })
  @ApiBadRequestResponse({
    description:
      'Cannot delete user due to constraints (e.g., has created questions)',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid or missing access token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions - requires super_admin role',
    type: ErrorResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: ErrorResponseDto,
  })
  @Delete(AuthRoutes.DELETE_USER)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteUser(
    @Param('userId', CustomParseUUIDPipe) userId: string,
    @User() currentUser: IUser,
  ): Promise<DeleteUserResponseDto> {
    try {
      // Additional security check: prevent users from deleting themselves via admin endpoint
      if (userId === currentUser.id) {
        throw new BadRequestException(
          'Cannot delete your own account using admin endpoint. Use the delete-account endpoint instead.',
        );
      }

      const result = await this.authService.deleteUser(userId, currentUser.id);
      return result;
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userId}:`, error.message);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Bulk Delete Users',
    description:
      'Deletes multiple users and all their related data. Only super_admin can perform this action. The deletion is processed in the background.',
  })
  @ApiOkResponse({
    description: 'Bulk deletion job queued successfully',
    type: BulkDeleteResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid request or no valid users found',
    type: ErrorResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized - invalid or missing token',
    type: ErrorResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - insufficient permissions',
    type: ErrorResponseDto,
  })
  @Delete(AuthRoutes.BULK_DELETE_USERS)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'user', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async bulkDeleteUsers(
    @Body() body: BulkDeleteUsersDto,
    @User() currentUser: IUser,
  ): Promise<BulkDeleteResponseDto> {
    try {
      // Ensure only super_admin can perform bulk deletions
      if (currentUser.role !== 'super_admin') {
        throw new BadRequestException(
          'Only super administrators can perform bulk user deletions',
        );
      }

      // Use admin email from request body if provided, otherwise use current user's email
      const adminEmail = body.adminEmail || currentUser.email;

      const result = await this.authService.bulkDeleteUsers(
        body.userIds,
        adminEmail,
        currentUser.id,
      );

      this.logger.log(
        `Bulk deletion job ${result.jobId} initiated by ${currentUser.email} for ${result.usersQueued} users`,
      );

      return {
        message: result.message,
        jobId: result.jobId,
        usersQueued: result.usersQueued,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to initiate bulk deletion by ${currentUser.email}:`,
        error.message,
      );
      throw error;
    }
  }
}
