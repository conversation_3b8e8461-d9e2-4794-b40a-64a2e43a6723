import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { User, selectUserSchema } from '@/db/schema/users';
import { querySchema } from '@/common/dto/query-params.dto';
import { ApiProperty } from '@nestjs/swagger';

const LoginInputSchema = z.object({
  email: z.string().email(),
  redirectUrl: z.string().url().optional(),
});

const otpSchema = z.object({
  otp: z.string().length(6),
});

const verifyMagicLinkSchema = z.object({
  token: z.string(),
});

export interface LoginResponse {
  accessToken: string;
  user: Pick<User, 'id' | 'email'>;
}

export class UserDto extends createZodDto(selectUserSchema) {}
export class OtpDto extends createZodDto(otpSchema) {}
export class VerifyMagicLinkDto extends createZodDto(verifyMagicLinkSchema) {}
export class LoginInputDto extends createZodDto(LoginInputSchema) {}
export const usersQueryParamsSchema = querySchema.extend({});
export class UserQueryParams extends createZodDto(usersQueryParamsSchema) {}

export const WaitingListResponseSchema = z.object({
  data: z.array(selectUserSchema),
  pagination: z.object({
    total: z.number().int(),
    page: z.number().int().positive(),
    limit: z.number().int().positive(),
    totalPages: z.number().int().positive(),
    hasNextPage: z.boolean(),
    hasPreviousPage: z.boolean(),
  }),
});
export type WaitingListResponse = z.infer<typeof WaitingListResponseSchema>;

export interface ApproveWaitingListResponse {
  total: number;
  data: Array<{
    id: string;
    email: string;
    state?: string;
  }>;
  message?: string;
}

// Define a separate class for the user object to avoid circular references
export class ApprovedUserDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User email address' })
  email: string;

  @ApiProperty({ description: 'User state', required: false })
  state?: string;

  constructor(data: { id: string; email: string; state?: string }) {
    this.id = data.id;
    this.email = data.email;
    if (data.state) {
      this.state = data.state;
    }
  }
}

export class ApproveWaitingListResponseDto {
  @ApiProperty({ description: 'Total number of users processed' })
  total: number;

  @ApiProperty({
    type: [ApprovedUserDto],
    description: 'Array of processed users',
  })
  data: ApprovedUserDto[];

  @ApiProperty({ description: 'Success message', required: false })
  message?: string;

  @ApiProperty({ description: 'HTTP status code', default: 200 })
  statusCode: number = 200;

  constructor(data: ApproveWaitingListResponse) {
    this.total = data.total;
    this.data = data.data.map((user) => new ApprovedUserDto(user));
    if (data.message) {
      this.message = data.message;
    }
  }
}

// Swagger Response DTOs
export class LoginResponseDto {
  @ApiProperty({
    description: 'Success message indicating OTP or magic link was sent',
    example: 'OTP sent successfully',
  })
  message!: string;
}

export class StudentProfileDto {
  @ApiProperty({ description: 'Student profile ID' })
  id!: string;

  @ApiProperty({ description: 'Student first name' })
  first_name!: string;

  @ApiProperty({ description: 'Student last name' })
  last_name!: string;

  @ApiProperty({ description: 'Associated user ID' })
  user_id!: string;
}

export class AdminProfileDto {
  @ApiProperty({ description: 'Admin profile ID' })
  id!: string;

  @ApiProperty({ description: 'Organization name' })
  name!: string;

  @ApiProperty({ description: 'Organization address', required: false })
  address?: string;

  @ApiProperty({ description: 'Associated user ID' })
  user_id!: string;
}

export class AuthUserDto {
  @ApiProperty({ description: 'User ID' })
  id!: string;

  @ApiProperty({ description: 'User email address' })
  email!: string;

  @ApiProperty({
    enum: ['student', 'student_admin', 'super_admin', 'admin'],
    description: 'User role',
    required: false,
  })
  role?: string;

  @ApiProperty({
    enum: ['active', 'disabled', 'inactive', 'verified', 'pending'],
    description: 'User state',
  })
  state!: string;

  @ApiProperty({ description: 'Profile picture URL', required: false })
  profile_pic_url?: string;

  @ApiProperty({
    type: AdminProfileDto,
    description: 'Admin profile data',
    required: false,
  })
  admin_profile?: AdminProfileDto;

  @ApiProperty({
    type: StudentProfileDto,
    description: 'Student profile data',
    required: false,
  })
  student_profile?: StudentProfileDto;

  @ApiProperty({ description: 'Whether user is deleted', required: false })
  deleted?: boolean;

  @ApiProperty({ description: 'Creation timestamp', required: false })
  created_at?: string;

  @ApiProperty({ description: 'Last update timestamp', required: false })
  updated_at?: string;

  @ApiProperty({ description: 'Deletion timestamp', required: false })
  deleted_at?: string;
}

export class VerifyOtpResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  accessToken!: string;

  @ApiProperty({ type: AuthUserDto, description: 'Authenticated user data' })
  user!: AuthUserDto;

  @ApiProperty({ description: 'Whether user account is deleted' })
  deleted!: boolean;
}

export class VerifyMagicLinkResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  accessToken!: string;

  @ApiProperty({ type: AuthUserDto, description: 'Authenticated user data' })
  user!: AuthUserDto;
}

export class RefreshTokenResponseDto {
  @ApiProperty({ description: 'New JWT access token' })
  accessToken!: string;
}

export class DeactivateAccountResponseDto {
  @ApiProperty({ description: 'Success message' })
  message!: string;
}

export class PaginationDto {
  @ApiProperty({ description: 'Total number of items' })
  total!: number;

  @ApiProperty({ description: 'Current page number' })
  page!: number;

  @ApiProperty({ description: 'Items per page' })
  limit!: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages!: number;

  @ApiProperty({ description: 'Whether there is a next page' })
  hasNextPage!: boolean;

  @ApiProperty({ description: 'Whether there is a previous page' })
  hasPreviousPage!: boolean;
}

export class WaitingListResponseDto {
  @ApiProperty({
    type: [AuthUserDto],
    description: 'Array of users in waiting list',
  })
  data!: AuthUserDto[];

  @ApiProperty({ type: PaginationDto, description: 'Pagination metadata' })
  pagination!: PaginationDto;
}

export class RefreshTokenRequestDto {
  @ApiProperty({ description: 'Refresh token' })
  refreshToken!: string;
}

export class ApproveUsersRequestDto {
  @ApiProperty({
    type: [String],
    description: 'Array of user IDs to approve',
    example: ['uuid1', 'uuid2', 'uuid3'],
  })
  userIds!: string[];
}

export class ErrorResponseDto {
  @ApiProperty({ description: 'HTTP status code' })
  statusCode!: number;

  @ApiProperty({
    oneOf: [{ type: 'string' }, { type: 'array', items: { type: 'string' } }],
    description: 'Error message(s)',
  })
  message!: string | string[];

  @ApiProperty({ description: 'Error type', required: false })
  error?: string;
}

export class DeletedUserDto {
  @ApiProperty({ description: 'User ID' })
  id!: string;

  @ApiProperty({ description: 'User email address' })
  email!: string;

  @ApiProperty({
    enum: ['student', 'student_admin', 'super_admin', 'admin'],
    description: 'User role',
  })
  role!: string;
}

export class DeleteUserResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'User and all related data deleted successfully',
  })
  message!: string;

  @ApiProperty({
    type: DeletedUserDto,
    description: 'Information about the deleted user',
    nullable: true,
  })
  deletedUser!: DeletedUserDto | null;
}

const deleteAccountConfirmationSchema = z.object({
  confirmationText: z
    .string()
    .min(1, 'Confirmation text is required')
    .refine(
      (val) =>
        val.toLowerCase() === 'delete' ||
        val.toLowerCase() === 'delete my account',
      'Must type exactly: "DELETE" or "delete my account"',
    ),
  password: z.string().min(1, 'Password is required').optional(),
});

export class DeleteAccountConfirmationDto extends createZodDto(
  deleteAccountConfirmationSchema,
) {}

export class DeleteAccountResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Your account and all related data have been permanently deleted',
  })
  message!: string;

  @ApiProperty({
    type: DeletedUserDto,
    description: 'Information about the deleted account',
  })
  deletedUser!: DeletedUserDto;
}

// Bulk deletion DTOs
const bulkDeleteUsersSchema = z.object({
  userIds: z
    .array(z.string().uuid('Invalid user ID format'))
    .min(1, 'At least one user ID is required')
    .max(100, 'Cannot delete more than 100 users at once'),
  adminEmail: z.string().email('Invalid email format').optional(),
});

export class BulkDeleteUsersDto extends createZodDto(bulkDeleteUsersSchema) {}

export class BulkDeletionStatsDto {
  @ApiProperty({ description: 'Total number of users processed' })
  totalProcessed!: number;

  @ApiProperty({ description: 'Number of users successfully deleted' })
  successCount!: number;

  @ApiProperty({ description: 'Number of users that failed to delete' })
  failureCount!: number;

  @ApiProperty({
    type: [DeletedUserDto],
    description: 'List of successfully deleted users',
  })
  deletedUsers!: DeletedUserDto[];

  @ApiProperty({
    type: [Object],
    description: 'List of failed deletions with reasons',
  })
  failures!: Array<{
    userId: string;
    email?: string;
    reason: string;
  }>;

  @ApiProperty({ description: 'Job completion timestamp' })
  completedAt!: string;

  @ApiProperty({ description: 'Admin email who initiated the deletion' })
  initiatedBy!: string;
}

export class BulkDeleteResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Bulk user deletion job has been queued successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Job ID for tracking the bulk deletion progress',
  })
  jobId!: string;

  @ApiProperty({
    description: 'Number of users queued for deletion',
  })
  usersQueued!: number;
}
